export type SecretKey =
	| "apiKey"
	| "clineAccountId"
	| "openRouterApiKey"
	| "awsAccessKey"
	| "awsSecretKey"
	| "awsSessionToken"
	| "awsBedrockApiKey"
	| "openAiApiKey"
	| "geminiApiKey"
	| "openAiNativeApiKey"
	| "deepSeekApiKey"
	| "requestyApiKey"
	| "togetherApiKey"
	| "fireworksApiKey"
	| "qwenApiKey"
	| "doubaoApiKey"
	| "mistralApiKey"
	| "liteLlmApiKey"
	| "authNonce"
	| "asksageApiKey"
	| "xaiApiKey"
	| "moonshotApiKey"
	| "huggingFaceApiKey"
	| "nebiusApiKey"
	| "sambanovaApiKey"
	| "cerebrasApiKey"
	| "sapAiCoreClientId"
	| "sapAiCoreClientSecret"
	| "groqApiKey"

export type GlobalStateKey =
	| "awsRegion"
	| "awsUseCrossRegionInference"
	| "awsBedrockUsePromptCache"
	| "awsBedrockEndpoint"
	| "awsProfile"
	| "awsBedrockApiKey"
	| "awsAuthentication"
	| "awsUseProfile"
	| "vertexProjectId"
	| "vertexRegion"
	| "lastShownAnnouncementId"
	| "taskHistory"
	| "openAiBaseUrl"
	| "openAiHeaders"
	| "ollamaBaseUrl"
	| "ollamaApiOptionsCtxNum"
	| "lmStudioBaseUrl"
	| "anthropicBaseUrl"
	| "geminiBaseUrl"
	| "azureApiVersion"
	| "openRouterProviderSorting"
	| "autoApprovalSettings"
	| "globalClineRulesToggles"
	| "globalWorkflowToggles"
	| "browserSettings"
	| "userInfo"
	| "liteLlmBaseUrl"
	| "liteLlmUsePromptCache"
	| "fireworksModelMaxCompletionTokens"
	| "fireworksModelMaxTokens"
	| "qwenApiLine"
	| "moonshotApiLine"
	| "mcpMarketplaceCatalog"
	| "telemetrySetting"
	| "asksageApiUrl"
	| "planActSeparateModelsSetting"
	| "enableCheckpointsSetting"
	| "mcpMarketplaceEnabled"
	| "favoritedModelIds"
	| "requestTimeoutMs"
	| "shellIntegrationTimeout"
	| "mcpResponsesCollapsed"
	| "terminalReuseEnabled"
	| "defaultTerminalProfile"
	| "isNewUser"
	| "welcomeViewCompleted"
	| "terminalOutputLineLimit"
	| "mcpDisplayMode"
	| "sapAiCoreTokenUrl"
	| "sapAiCoreBaseUrl"
	| "sapAiResourceGroup"
	| "claudeCodePath"
	// Settings around plan/act and ephemeral model configuration
	| "chatSettings"
	| "mode"
	// Plan mode configurations
	| "planModeApiProvider"
	| "planModeApiModelId"
	| "planModeThinkingBudgetTokens"
	| "planModeReasoningEffort"
	| "planModeVsCodeLmModelSelector"
	| "planModeAwsBedrockCustomSelected"
	| "planModeAwsBedrockCustomModelBaseId"
	| "planModeOpenRouterModelId"
	| "planModeOpenRouterModelInfo"
	| "planModeOpenAiModelId"
	| "planModeOpenAiModelInfo"
	| "planModeOllamaModelId"
	| "planModeLmStudioModelId"
	| "planModeLiteLlmModelId"
	| "planModeLiteLlmModelInfo"
	| "planModeRequestyModelId"
	| "planModeRequestyModelInfo"
	| "planModeTogetherModelId"
	| "planModeFireworksModelId"
	| "planModeSapAiCoreModelId"
	| "planModeGroqModelId"
	| "planModeGroqModelInfo"
	| "planModeHuggingFaceModelId"
	| "planModeHuggingFaceModelInfo"
	// Act mode configurations
	| "actModeApiProvider"
	| "actModeApiModelId"
	| "actModeThinkingBudgetTokens"
	| "actModeReasoningEffort"
	| "actModeVsCodeLmModelSelector"
	| "actModeAwsBedrockCustomSelected"
	| "actModeAwsBedrockCustomModelBaseId"
	| "actModeOpenRouterModelId"
	| "actModeOpenRouterModelInfo"
	| "actModeOpenAiModelId"
	| "actModeOpenAiModelInfo"
	| "actModeOllamaModelId"
	| "actModeLmStudioModelId"
	| "actModeLiteLlmModelId"
	| "actModeLiteLlmModelInfo"
	| "actModeRequestyModelId"
	| "actModeRequestyModelInfo"
	| "actModeTogetherModelId"
	| "actModeFireworksModelId"
	| "actModeSapAiCoreModelId"
	| "actModeGroqModelId"
	| "actModeGroqModelInfo"
	| "actModeHuggingFaceModelId"
	| "actModeHuggingFaceModelInfo"

export type LocalStateKey = "localClineRulesToggles" | "localCursorRulesToggles" | "localWindsurfRulesToggles" | "workflowToggles"
