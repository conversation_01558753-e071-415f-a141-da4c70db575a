---
title: "Installing Cline"
description: "Cline is a VS Code extension that brings AI-powered coding assistance directly
    to your editor. Install using one of these methods:"
---

### Installation Options

-   **VS Code Marketplace (Recommended):** Fastest method for standard VS Code and Cursor users.
-   **Open VSX Registry:** For VS Code-compatible editors like VSCodium.

### 🛠️ VS Code Marketplace: Step-by-Step Setup

Follow these steps to get Cline up and running:

1.  **Open VS Code:** Launch the VS Code application.

    > ⚠️ **Note:** If VS Code shows "Running extensions might...", click "Allow".

2.  **Open Your Cline Folder:** In VS Code, open the Cline folder you created in Documents.
3.  **Navigate to Extensions:** Click on the Extensions icon in the Activity Bar on the side of VS Code (`Ctrl + Shift + X` or `Cmd + Shift + X`).
4.  **Search for 'Cline':** In the Extensions search bar, type `Cline`.

<Frame caption="VS Code marketplace with Cline extension ready to install">
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(20).png"
		alt="VS Code marketplace showing Cline extension"
	/>
</Frame>

1. **Install the Extension:** Click the "Install" button next to the Cline extension.
2. **Open Cline:**
    - Click the Cline icon in the Activity Bar.
    - Or, use the command palette (`Ctrl/Cmd + Shift + P`) and type "Cline: Open In New Tab" for a better view.
3. **Troubleshooting:** If you don't see the Cline icon, try restarting VS Code.

> ✅ **Pro Tip:** You should see the Cline chat window appear in your VS Code editor!

### 🌐 Open VSX Registry

For VS Code-compatible editors without Marketplace access (like VSCodium and Windsurf):

1. Open your editor.
2. Access the Extensions view.
3. Search for "Cline".
4. Select "Cline" by saoudrizwan and click **Install**.
5. Reload if prompted.

### 👤 Creating Your Cline Account

Now that you have Cline installed, let's get you set up with your account:

1. **Sign In to Cline:**
    - Click the **Sign In** button in the Cline extension.
    - You'll be taken to [app.cline.bot](https://app.cline.bot) to create your account.
2. **Start with Free Credits:**
    - No credit card needed!
3. **Available AI Models:**
    - Anthropic Claude 3.5-Sonnet (recommended for coding)
    - DeepSeek Chat (cost-effective alternative)
    - Google Gemini 2.0 Flash
    - And more — all through your Cline account.

### 💻 Your First Interaction with Cline

You're ready to start building! Copy and paste this prompt into the Cline chat window:

```
Hey Cline! Could you help me create a new project folder called "hello-world" in my Cline directory and make a simple webpage that says "Hello World" in big blue text?
```

> ✅ **Pro Tip:** Cline will help you create the project folder and set up your first webpage!

### 🧩 Tips for Working with Cline

-   **Ask Questions:** If you're unsure about something, ask Cline!
-   **Use Screenshots:** Cline can understand images — show him what you're working on.
-   **Copy and Paste Errors:** Share error messages in the chat for solutions.
-   **Speak Plainly:** Use your own words — Cline will translate them into code.

### 🫂 Still Struggling?

Join our Discord community and engage with our team and other Cline users directly.
