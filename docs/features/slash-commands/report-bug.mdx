---
title: "Report Bug Command"
sidebarTitle: "/reportbug"
---

`/reportbug` is an absolute lifesaver when you hit a weird issue with <PERSON><PERSON>. Instead of having to remember all the details GitHub wants for a bug report, this command turns <PERSON><PERSON> into your personal bug reporting assistant.

It walks you through collecting all the info needed for a proper bug report and then shoots it straight to our GitHub issues page with all the right formatting and system details included.

#### Using the `/reportbug` Slash Command

When you run into something funky that doesn't seem right:

-   Just type `/reportbug` in the chat
-   Cline will guide you through all the details we need:
    -   A quick title describing the issue
    -   What actually happened vs. what you expected
    -   Steps to reproduce the bug
    -   Any relevant output or errors you saw
    -   Additional context that might help us fix it
-   You'll get to review everything before it's submitted
-   Once you approve, it opens a perfectly formatted GitHub issue with all your info plus automatic system details

#### Example

Last week I hit a weird bug where <PERSON><PERSON> kept timing out when reading large files. Instead of trying to remember all the GitHub template fields, I just typed `/reportbug` and <PERSON><PERSON> guided me through the whole process.

It asked me about what I was trying to do, what happened instead, and the exact steps that led to the issue. The best part was that it automatically included my OS version, Cline version, and all the technical details our devs would need.

A few seconds later, I had a properly formatted GitHub issue created without having to hunt down any of that info myself.
