---
title: "Commands & Shortcuts Overview"
sidebarTitle: "Overview"
---

<PERSON><PERSON> integrates directly into VSCode's interface, letting you access AI assistance without disrupting your workflow. These integrations appear as commands in context menus, keyboard shortcuts, and quick fixes throughout the editor.

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/editor-integration.png"
		alt="Editor Integration Overview"
	/>
</Frame>

### What are Editor Integrations?

Editor integrations are commands and shortcuts that let you use Cline right where you're working. Instead of switching to the Cline panel first, you can select code, right-click, and immediately send it to Cline for help.
These integrations appear in different places throughout VSCode:

-   In the editor context menu (right-click menu) - "Add to Cline"
-   In the terminal context menu - "Add to Cline"
-   In the Source Control view - "Generate Commit Message"
-   As keyboard shortcuts - Various Cline commands
-   As Quick Fix options (lightbulb menu) - "Fix with Cline", "Explain with Cline", "Improve with Cline"

### Available Editor Integrations

Cline offers several editor integrations, each designed to enhance different aspects of your development workflow:

<Columns cols={2}>
  <Card title="Code Commands" icon="code" href="/features/commands-and-shortcuts/code-commands">
    Right-click on code to add it to Cline, or use the lightbulb menu to fix errors, explain code, or improve it. Cline sees the complete code context, including imports and surrounding functions.
  </Card>

{" "}

<Card title="Terminal Integration" icon="terminal" href="/features/commands-and-shortcuts/terminal-integration">
	Add terminal output to Cline with a right-click or use `@terminal` mentions. Perfect for debugging build errors, test
	failures, or runtime issues.
</Card>

{" "}

<Card title="Git Integration" icon="code-branch" href="/features/commands-and-shortcuts/git-integration">
	Generate commit messages, explain diffs, or analyze changes with Cline's Git integration. Cline understands your version
	control context.
</Card>

{" "}

<Card title="Keyboard Shortcuts" icon="keyboard" href="/features/commands-and-shortcuts/keyboard-shortcuts">
	Speed up your workflow with keyboard shortcuts for common Cline actions. Quickly add code to chat, fix errors, or improve your code.
</Card>
</Columns>

### How They Work

When you use these commands, Cline:

-   Captures the relevant context (selected code, file path, terminal output, etc.)
-   Focuses the Cline interface
-   Creates a conversation with the captured context
-   In some cases, automatically generates a suggested prompt

Behind the scenes, these commands use VSCode's extension API to register commands, access editor state, and control VSCode's interface.
