---
title: "DeepSeek"
description: "Learn how to configure and use DeepSeek models like deepseek-chat and deepseek-reasoner with Cline."
---

<PERSON>line supports accessing models through the DeepSeek API, including `deepseek-chat` and `deepseek-reasoner`.

**Website:** [https://platform.deepseek.com/](https://platform.deepseek.com/)

### Getting an API Key

1.  **Sign Up/Sign In:** Go to the [DeepSeek Platform](https://platform.deepseek.com/). Create an account or sign in.
2.  **Navigate to API Keys:** Find your API keys in the [API keys](https://platform.deepseek.com/api_keys) section of the platform.
3.  **Create a Key:** Click "Create new API key". Give your key a descriptive name (e.g., "Cline").
4.  **Copy the Key:** **Important:** Copy the API key _immediately_. You will not be able to see it again. Store it securely.

### Supported Models

Cline supports the following DeepSeek models:

-   `deepseek-v3-0324` (Recommended for coding tasks)
-   `deepseek-r1` (Recommended for reasoning tasks)

### Configuration in Cline

1.  **Open Cline Settings:** Click the ⚙️ icon in the Cline panel.
2.  **Select Provider:** Choose "DeepSeek" from the "API Provider" dropdown.
3.  **Enter API Key:** Paste your DeepSeek API key into the "DeepSeek API Key" field.
4.  **Select Model:** Choose your desired model from the "Model" dropdown.

### Tips and Notes

-   **Pricing:** Refer to the [DeepSeek Pricing](https://api-docs.deepseek.com/quick_start/pricing/) page for details on model costs.
