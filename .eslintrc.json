{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "plugins": ["@typescript-eslint", "eslint-rules"], "rules": {"@typescript-eslint/naming-convention": ["warn", {"selector": "import", "format": ["camelCase", "PascalCase"]}], "@typescript-eslint/semi": "off", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "semi": "off", "react-hooks/exhaustive-deps": "off", "eslint-rules/no-direct-vscode-api": "warn", "no-restricted-syntax": ["error", {"selector": "VariableDeclarator[id.type=\"ObjectPattern\"][init.object.name=\"process\"][init.property.name=\"env\"]", "message": "Use process.env.VARIABLE_NAME directly instead of destructuring"}]}, "ignorePatterns": ["out", "dist", "**/*.d.ts"]}