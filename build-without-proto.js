#!/usr/bin/env node

// Temporary build script that skips proto compilation
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Building Cline extension without proto compilation...');

// Create empty proto directories if they don't exist
const protoDir = path.join(__dirname, 'src/shared/proto');
if (!fs.existsSync(protoDir)) {
    fs.mkdirSync(protoDir, { recursive: true });
    // Create an empty index.ts file
    fs.writeFileSync(path.join(protoDir, 'index.ts'), '// Proto files will be generated here\nexport {};\n');
}

try {
    console.log('Building webview...');
    execSync('cd webview-ui && npm run build', { stdio: 'inherit' });
    
    console.log('Running TypeScript compilation...');
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    
    console.log('Running ESBuild...');
    execSync('node esbuild.mjs --production', { stdio: 'inherit' });
    
    console.log('Build completed successfully!');
} catch (error) {
    console.error('Build failed:', error.message);
    process.exit(1);
}
