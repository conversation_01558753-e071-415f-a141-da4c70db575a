name: E2E Tests

on:
    push:
        branches:
            - main
    pull_request:
        types: [opened, reopened, synchronize, ready_for_review]
    workflow_dispatch:

concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    matrix_prep:
        runs-on: ubuntu-latest
        outputs:
            matrix: ${{ steps.set-matrix.outputs.matrix }}
        steps:
            - id: set-matrix
              run: |
                  echo 'matrix=[{"runner":"ubuntu"},{"runner":"windows"},{"runner":"macos"}]' >> $GITHUB_OUTPUT

    e2e:
        needs: matrix_prep
        strategy:
            fail-fast: false
            matrix:
                include: ${{ fromJson(needs.matrix_prep.outputs.matrix) }}
        runs-on: ${{ matrix.runner }}-latest
        timeout-minutes: 20
        permissions:
            id-token: write
            contents: read
        steps:
            - uses: actions/checkout@v4
            - name: Setup Node.js environment
              uses: actions/setup-node@v4
              with:
                  node-version: 22

            # Cache root dependencies - only reuse if package-lock.json exactly matches
            - name: Cache root dependencies
              uses: actions/cache@v4
              id: root-cache
              with:
                  path: node_modules
                  key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}

            # Cache webview-ui dependencies - only reuse if package-lock.json exactly matches
            - name: Cache webview-ui dependencies
              uses: actions/cache@v4
              id: webview-cache
              with:
                  path: webview-ui/node_modules
                  key: ${{ runner.os }}-npm-webview-${{ hashFiles('webview-ui/package-lock.json') }}

            # Cache VS Code installation
            - name: Cache VS Code
              uses: actions/cache@v4
              id: vscode-cache
              with:
                  path: .vscode-test
                  key: vscode-${{ runner.os }}-stable-${{ hashFiles('.vscode-test.mjs', 'package.json') }}
                  restore-keys: |
                      vscode-${{ runner.os }}-stable-

            # Cache Playwright browsers
            - name: Cache Playwright browsers
              uses: actions/cache@v4
              id: playwright-cache
              with:
                  path: |
                      ~/.cache/ms-playwright
                      ~/Library/Caches/ms-playwright
                      ~/AppData/Local/ms-playwright
                  key: playwright-browsers-${{ runner.os }}-${{ hashFiles('package-lock.json') }}
                  restore-keys: |
                      playwright-browsers-${{ runner.os }}-

            - name: Install root dependencies
              if: steps.root-cache.outputs.cache-hit != 'true'
              run: npm ci

            - name: Install webview-ui dependencies
              if: steps.webview-cache.outputs.cache-hit != 'true'
              run: cd webview-ui && npm ci

            - name: Install xvfb on Linux
              if: matrix.runner == 'ubuntu'
              run: sudo apt-get update && sudo apt-get install -y xvfb

            # Run optimized E2E tests (eliminates redundant builds)
            - name: Run E2E tests - Linux
              if: matrix.runner == 'ubuntu'
              run: xvfb-run -a npm run test:e2e:optimal

            - name: Run E2E tests - Non-Linux
              if: matrix.runner != 'ubuntu'
              run: npm run test:e2e:optimal

            - uses: actions/upload-artifact@v4
              if: ${{ failure() }}
              with:
                  name: playwright-recordings-${{ matrix.runner }}
                  path: |
                      test-results/playwright/
