name: "Publish Release"

on:
    workflow_dispatch:
        inputs:
            release-type:
                description: "Choose release type (release or pre-release)"
                required: true
                default: "release"
                type: choice
                options:
                    - pre-release
                    - release
            tag:
                description: "Enter existing tag to publish (e.g., v3.1.2)"
                required: true
                type: string

permissions:
    contents: write
    packages: write
    checks: write
    pull-requests: write

jobs:
    test:
        uses: ./.github/workflows/test.yml

    publish:
        needs: test
        name: Publish Extension
        runs-on: ubuntu-latest
        environment: publish

        steps:
            - uses: actions/checkout@v4
              with:
                  ref: ${{ github.event.inputs.tag }}

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: "lts/*"

            # Cache root dependencies - only reuse if package-lock.json exactly matches
            - name: Cache root dependencies
              uses: actions/cache@v4
              id: root-cache
              with:
                  path: node_modules
                  key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}

            # Cache webview-ui dependencies - only reuse if package-lock.json exactly matches
            - name: Cache webview-ui dependencies
              uses: actions/cache@v4
              id: webview-cache
              with:
                  path: webview-ui/node_modules
                  key: ${{ runner.os }}-npm-webview-${{ hashFiles('webview-ui/package-lock.json') }}

            - name: Install root dependencies
              if: steps.root-cache.outputs.cache-hit != 'true'
              run: npm ci --include=optional

            - name: Install webview-ui dependencies
              if: steps.webview-cache.outputs.cache-hit != 'true'
              run: cd webview-ui && npm ci --include=optional

            - name: Install Publishing Tools
              run: npm install -g @vscode/vsce ovsx

            - name: Get Version
              id: get_version
              run: |
                  VERSION=$(node -p "require('./package.json').version")
                  echo "version=$VERSION" >> $GITHUB_OUTPUT

            - name: Validate Tag
              id: validate_tag
              run: |
                  TAG="${{ github.event.inputs.tag }}"
                  echo "tag=$TAG" >> $GITHUB_OUTPUT
                  echo "Using existing tag: $TAG"

                  # Verify the tag exists
                  if ! git rev-parse "$TAG" >/dev/null 2>&1; then
                    echo "Error: Tag '$TAG' does not exist in the repository"
                    exit 1
                  fi

                  echo "Tag '$TAG' validated successfully"

            - name: Package and Publish Extension
              env:
                  VSCE_PAT: ${{ secrets.VSCE_PAT }}
                  OVSX_PAT: ${{ secrets.OVSX_PAT }}
                  CLINE_ENVIRONMENT: production
              run: |
                  # Required to generate the .vsix
                  vsce package --out "cline-${{ steps.get_version.outputs.version }}.vsix"

                  if [ "${{ github.event.inputs.release-type }}" = "pre-release" ]; then
                    npm run publish:marketplace:prerelease
                    echo "Successfully published pre-release version ${{ steps.get_version.outputs.version }} to VS Code Marketplace and Open VSX Registry"
                  else
                    npm run publish:marketplace
                    echo "Successfully published release version ${{ steps.get_version.outputs.version }} to VS Code Marketplace and Open VSX Registry"
                  fi

            # - name: Get Changelog Entry
            #   id: changelog
            #   uses: mindsers/changelog-reader-action@v2
            #   with:
            #       # This expects a standard Keep a Changelog format
            #       # "latest" means it will read whichever is the most recent version
            #       # set in "## [1.2.3] - 2025-01-28" style
            #       version: latest

            - name: Create GitHub Release
              uses: softprops/action-gh-release@v1
              with:
                  tag_name: ${{ steps.validate_tag.outputs.tag }}
                  files: "*.vsix"
                  # body: ${{ steps.changelog.outputs.content }}
                  generate_release_notes: true
                  prerelease: ${{ github.event.inputs.release-type == 'pre-release' }}
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
